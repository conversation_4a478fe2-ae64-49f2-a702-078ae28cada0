#!/bin/bash

# 多交易所持仓可视化项目一键部署脚本
# 使用方法: ./deploy.sh <ssh_host> [port]
# 例如: ./deploy.sh live-analyze 5000

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -lt 1 ]; then
    print_error "使用方法: $0 <ssh_host> [port]"
    print_info "例如: $0 live-analyze 5000"
    print_info "支持SSH配置别名和完整地址"
    exit 1
fi

SSH_HOST=$1
PORT=${2:-5000}  # 默认端口5000
PROJECT_NAME="caokong_vz"

print_info "🚀 开始部署到: $SSH_HOST"
print_info "服务端口: $PORT"

# 1. 检查本地文件
print_info "1️⃣ 检查本地项目文件..."
required_files=("app.py" "requirements.txt" "templates/index.html" "static/style.css" "static/script.js" "test_client.py")
for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        print_error "缺少必要文件: $file"
        exit 1
    fi
done
print_success "本地文件检查完成"

# 2. 检查SSH连接
print_info "2️⃣ 测试SSH连接..."
if ! ssh -o ConnectTimeout=10 -o LogLevel=QUIET "$SSH_HOST" "echo 'SSH连接成功'" > /dev/null 2>&1; then
    print_error "无法连接到 $SSH_HOST，请检查SSH配置"
    print_info "提示: 确保SSH密钥已配置或使用 ssh-copy-id $SSH_HOST"
    exit 1
fi
print_success "SSH连接正常"

# 3. 获取远程环境信息
print_info "3️⃣ 检测远程环境..."
REMOTE_INFO=$(ssh -o LogLevel=QUIET "$SSH_HOST" "
    echo \"HOME=\$HOME\"
    echo \"USER=\$USER\"
    echo \"HOSTNAME=\$(hostname)\"
    echo \"PYTHON=\$(which python3 2>/dev/null || which python 2>/dev/null || echo 'none')\"
    echo \"PIP=\$(which pip 2>/dev/null || which pip3 2>/dev/null || echo 'none')\"
")

# 解析远程信息
eval "$REMOTE_INFO"
print_success "远程环境检测完成"
echo "  - 用户: $USER"
echo "  - 主机: $HOSTNAME"
echo "  - Python: $PYTHON"
echo "  - Pip: $PIP"

# 4. 选择部署目录
print_info "4️⃣ 选择部署目录..."
POSSIBLE_DIRS=(
    "$HOME/deploy/$PROJECT_NAME"
    "$HOME/tmp/$PROJECT_NAME"
    "$HOME/$PROJECT_NAME"
)

REMOTE_DIR=""
for dir in "${POSSIBLE_DIRS[@]}"; do
    if ssh -o LogLevel=QUIET "$SSH_HOST" "mkdir -p \"$dir\" 2>/dev/null" > /dev/null 2>&1; then
        REMOTE_DIR="$dir"
        break
    fi
done

if [ -z "$REMOTE_DIR" ]; then
    print_error "无法创建部署目录"
    exit 1
fi

print_success "部署目录: $REMOTE_DIR"

# 5. 停止现有服务
print_info "5️⃣ 停止现有服务..."
ssh -o LogLevel=QUIET "$SSH_HOST" << 'EOF' > /dev/null 2>&1
    # 停止现有服务
    pkill -f 'python.*app.py' 2>/dev/null || true
    if [ -f '$REMOTE_DIR/app.pid' ]; then
        kill $(cat '$REMOTE_DIR/app.pid') 2>/dev/null || true
        rm -f '$REMOTE_DIR/app.pid'
    fi
    sleep 2
EOF
print_success "现有服务已停止"

# 6. 创建目录并上传文件
print_info "6️⃣ 上传项目文件..."
ssh -o LogLevel=QUIET "$SSH_HOST" "mkdir -p '$REMOTE_DIR'/{templates,static,logs}" > /dev/null 2>&1

scp -q -r app.py requirements.txt templates/ static/ test_client.py "$SSH_HOST:$REMOTE_DIR/"
if [ -f "README.md" ]; then
    scp -q README.md "$SSH_HOST:$REMOTE_DIR/"
fi
print_success "文件上传完成"

# 7. 安装依赖和启动服务
print_info "7️⃣ 安装依赖和启动服务..."
ssh -o LogLevel=QUIET "$SSH_HOST" << 'EOF' > /dev/null  2>&1
# 抑制欢迎信息
exec 2>/dev/null
cd '$REMOTE_DIR'

# 创建虚拟环境（如果不存在）
if [ ! -d venv ]; then
    echo '创建Python虚拟环境...'
    python3 -m venv venv
fi

# 激活虚拟环境
source venv/bin/activate

# 安装依赖
echo '安装Python依赖...'
pip install -r requirements.txt > /dev/null

# 修改配置以支持外部访问
echo '配置服务参数...'
# 确保服务绑定到0.0.0.0以允许外部访问
sed -i "s/host='127.0.0.1'/host='0.0.0.0'/g" app.py 2>/dev/null || true
sed -i "s/host=\"127.0.0.1\"/host=\"0.0.0.0\"/g" app.py 2>/dev/null || true
sed -i "s/host=127.0.0.1/host=0.0.0.0/g" app.py 2>/dev/null || true

# 修改端口配置（如果需要）
if [ '$PORT' != '5000' ]; then
    sed -i "s/port=5000/port=$PORT/g" app.py
fi

# 启动服务
echo '启动服务...'
nohup python app.py > logs/service.log 2>&1 &
echo \$! > app.pid

# 等待服务启动
sleep 3

# 检查服务状态
if kill -0 \$(cat app.pid) 2>/dev/null; then
    echo '✅ 服务启动成功'
    echo "PID: \$(cat app.pid)"
    # 获取IP地址用于显示（支持IMDSv1和IMDSv2）
    PUBLIC_IP=''

    # 首先尝试IMDSv2（需要token）
    TOKEN=\$(curl -s --connect-timeout 5 -X PUT \"http://***************/latest/api/token\" -H \"X-aws-ec2-metadata-token-ttl-seconds: 21600\" 2>/dev/null || echo '')
    if [ -n \"\$TOKEN\" ] && [ \"\$TOKEN\" != \"\" ] && ! echo \"\$TOKEN\" | grep -q '<html>'; then
        PUBLIC_IP=\$(curl -s --connect-timeout 5 -H \"X-aws-ec2-metadata-token: \$TOKEN\" http://***************/latest/meta-data/public-ipv4 2>/dev/null || echo '')
    fi

    # 如果IMDSv2失败，尝试IMDSv1
    if [ -z \"\$PUBLIC_IP\" ] || echo \"\$PUBLIC_IP\" | grep -q '<html>'; then
        PUBLIC_IP=\$(curl -s --connect-timeout 5 http://***************/latest/meta-data/public-ipv4 2>/dev/null || echo '')
    fi

    # 检查是否获取到有效的IP地址
    if [ -n \"\$PUBLIC_IP\" ] && [ \"\$PUBLIC_IP\" != \"\" ] && ! echo \"\$PUBLIC_IP\" | grep -q '<html>' && echo \"\$PUBLIC_IP\" | grep -E '^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$' >/dev/null; then
        echo "访问地址: http://\$PUBLIC_IP:$PORT (公网IP)"
    else
        echo "访问地址: http://\$(hostname -I | awk '{print \$1}'):$PORT (内网IP)"
    fi
else
    echo '❌ 服务启动失败'
    cat logs/service.log
    exit 1
fi
EOF

if [ $? -eq 0 ]; then
    print_success "服务启动成功！"
else
    print_error "服务启动失败"
    exit 1
fi

# 8. 验证部署
print_info "8️⃣ 验证部署..."
sleep 2

# 获取服务器IP（优先使用EC2公网IP）
print_info "获取服务器IP地址..."
SERVER_IP=$(ssh -o LogLevel=QUIET "$SSH_HOST" "
    # 尝试获取EC2公网IP（支持IMDSv1和IMDSv2）
    PUBLIC_IP=''

    # 首先尝试IMDSv2（需要token）
    TOKEN=\$(curl -s --connect-timeout 5 -X PUT \"http://***************/latest/api/token\" -H \"X-aws-ec2-metadata-token-ttl-seconds: 21600\" 2>/dev/null || echo '')
    if [ -n \"\$TOKEN\" ] && [ \"\$TOKEN\" != \"\" ] && ! echo \"\$TOKEN\" | grep -q '<html>'; then
        PUBLIC_IP=\$(curl -s --connect-timeout 5 -H \"X-aws-ec2-metadata-token: \$TOKEN\" http://***************/latest/meta-data/public-ipv4 2>/dev/null || echo '')
    fi

    # 如果IMDSv2失败，尝试IMDSv1
    if [ -z \"\$PUBLIC_IP\" ] || echo \"\$PUBLIC_IP\" | grep -q '<html>'; then
        PUBLIC_IP=\$(curl -s --connect-timeout 5 http://***************/latest/meta-data/public-ipv4 2>/dev/null || echo '')
    fi

    # 检查是否获取到有效的IP地址
    if [ -n \"\$PUBLIC_IP\" ] && [ \"\$PUBLIC_IP\" != \"\" ] && ! echo \"\$PUBLIC_IP\" | grep -q '<html>' && echo \"\$PUBLIC_IP\" | grep -E '^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$' >/dev/null; then
        echo \"\$PUBLIC_IP\"
    else
        # 如果不是EC2或无法获取公网IP，使用内网IP
        hostname -I | awk '{print \$1}'
    fi
")

if [ -z "$SERVER_IP" ]; then
    print_error "无法获取服务器IP地址"
    exit 1
fi

print_info "服务器IP: $SERVER_IP"

# 测试Web服务
print_info "测试Web服务连通性..."
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 10 "http://$SERVER_IP:$PORT" 2>/dev/null || echo "000")

if [ "$HTTP_CODE" = "200" ]; then
    print_success "Web服务响应正常 (HTTP $HTTP_CODE)"
elif [ "$HTTP_CODE" = "000" ]; then
    print_warning "无法连接到Web服务，可能是防火墙或安全组限制"
    print_info "请检查以下配置："
    print_info "  - EC2安全组是否开放端口 $PORT"
    print_info "  - 服务器防火墙是否允许端口 $PORT"
    print_info "  - 服务是否正确启动在 0.0.0.0:$PORT"
else
    print_warning "Web服务响应异常 (HTTP $HTTP_CODE)，可能需要等待更长时间"
fi

# 9. 部署完成
print_success "🎉 部署完成！"
